import { useState, useCallback, useEffect } from "react";
import { useRouter } from "expo-router";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getUserDetailsAction } from "@/store/actions/auth";
import type { Address, AddressCollection } from "@/types/cart";

export const useAddressManagement = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.auth.user);

  // Address state
  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState<
    number | null
  >(null);
  const [selectedShippingAddressId, setSelectedShippingAddressId] = useState<
    number | null
  >(null);
  const [useBillingAsShipping, setUseBillingAsShipping] =
    useState<boolean>(true);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState<boolean>(false);

  const fetchUserDetails = useCallback(async () => {
    try {
      setIsLoadingAddresses(true);
      await dispatch(getUserDetailsAction({})).unwrap();
    } catch (error) {
      console.error("Failed to fetch user details:", error);
    } finally {
      setIsLoadingAddresses(false);
    }
  }, [dispatch]);

  const getAddresses = useCallback((): AddressCollection => {
    const allAddresses = user?.address || [];
    const billingAddresses = allAddresses.filter(
      (addr: any) => addr.billing_shipping === "1"
    );
    const shippingAddresses = allAddresses.filter(
      (addr: any) => addr.billing_shipping === "2"
    );

    return {
      all: allAddresses,
      billing: billingAddresses,
      shipping: shippingAddresses,
    };
  }, [user?.address]);

  const handleBillingAddressSelection = useCallback((address: Address) => {
    setSelectedBillingAddressId(address.id);
  }, []);

  const handleShippingAddressSelection = useCallback((address: Address) => {
    setSelectedShippingAddressId(address.id);
  }, []);

  const handleToggleBillingAsShipping = useCallback((value: boolean) => {
    setUseBillingAsShipping(value);
    // Clear shipping address selection when using billing as shipping
    if (value) {
      setSelectedShippingAddressId(null);
    }
  }, []);

  const handleEditAddress = useCallback(
    (addressId: number) => {
      router.push(`/(protected)/add-address?address_id=${addressId}`);
    },
    [router]
  );

  const getEffectiveShippingAddressId = useCallback(() => {
    return useBillingAsShipping
      ? selectedBillingAddressId
      : selectedShippingAddressId;
  }, [
    useBillingAsShipping,
    selectedBillingAddressId,
    selectedShippingAddressId,
  ]);

  const getSelectedBillingAddress = useCallback((): Address | null => {
    const addresses = getAddresses();
    return (
      addresses.billing.find(
        (addr: Address) => addr.id === selectedBillingAddressId
      ) || null
    );
  }, [getAddresses, selectedBillingAddressId]);

  const getSelectedShippingAddress = useCallback((): Address | null => {
    const addresses = getAddresses();
    const effectiveShippingId = getEffectiveShippingAddressId();
    return (
      addresses.all.find((addr: Address) => addr.id === effectiveShippingId) ||
      null
    );
  }, [getAddresses, getEffectiveShippingAddressId]);

  const isReadyForCheckout = useCallback(() => {
    const hasRequiredBillingAddress = !!selectedBillingAddressId;

    if (useBillingAsShipping) {
      return hasRequiredBillingAddress;
    }

    const hasRequiredShippingAddress = !!selectedShippingAddressId;
    return hasRequiredBillingAddress && hasRequiredShippingAddress;
  }, [
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
  ]);

  // Set default addresses when user data is available
  useEffect(() => {
    if (user?.address && Array.isArray(user.address)) {
      // Set default billing address
      const billingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "1"
      );
      if (billingAddresses.length > 0 && !selectedBillingAddressId) {
        setSelectedBillingAddressId(billingAddresses[0].id);
      }

      // Set default shipping address
      const shippingAddresses = user.address.filter(
        (addr: any) => addr.billing_shipping === "2"
      );
      if (shippingAddresses.length > 0 && !selectedShippingAddressId) {
        setSelectedShippingAddressId(shippingAddresses[0].id);
      }
    }
  }, [user, selectedBillingAddressId, selectedShippingAddressId]);

  return {
    // State
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    isLoadingAddresses,

    // Actions
    handleBillingAddressSelection,
    handleShippingAddressSelection,
    handleToggleBillingAsShipping,
    handleEditAddress,
    fetchUserDetails,

    // Computed values
    getAddresses,
    getEffectiveShippingAddressId,
    getSelectedBillingAddress,
    getSelectedShippingAddress,
    isReadyForCheckout,
  };
};
