import * as ImagePicker from "expo-image-picker";

export enum PaymentMethod {
  Account_Number = 1,
  UPI_Id = 2,
  QR_Code = 3,
  Credit = 4,
}

export interface PaymentMethodData {
  id: PaymentMethod;
  label: string;
  description?: string;
  icon: string;
  details: Record<string, any>;
}

export interface PaymentStepProps {
  onBack: () => void;
  onContinue: (
    selectedMethod: PaymentMethod,
    paymentProof?: ImagePicker.ImagePickerAsset,
    selectedPaymentDetails?: any
  ) => void;
}

// Payment methods that require proof
export const PAYMENT_METHODS_REQUIRING_PROOF = [
  PaymentMethod.Account_Number,
  PaymentMethod.UPI_Id,
  PaymentMethod.QR_Code,
];
