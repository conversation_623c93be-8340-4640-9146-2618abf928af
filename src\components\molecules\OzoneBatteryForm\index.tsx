import React from "react";
import { View, Text, ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  FormInputGroup,
  FormLabel,
  FormInput,
  QRCodeSection,
  QRCodeButton,
  QRCodeButtonText,
  QRCodeButtonContent,
  QRCodeButtonTextContainer,
  QRCodeButtonSubtitle,
  QRCodeButtonTitle,
} from "@/styles/Complaint.styles";
import { QRScannerModal } from "../QRScannerModal";

interface OzoneBatteryFormProps {
  serialNumber: string;
  onQRCodeChange: (value: string) => void;
  onSerialNumberChange: (value: string) => void;
  isLoadingQRCode?: boolean;
}

export const OzoneBatteryForm: React.FC<OzoneBatteryFormProps> = ({
  serialNumber,
  onQRCodeChange,
  onSerialNumberChange,
  isLoadingQRCode = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [showScanner, setShowScanner] = React.useState(false);

  const handleScanQRCode = () => {
    if (!isLoadingQRCode) {
      setShowScanner(true);
    }
  };

  return (
    <>
      <QRCodeSection>
        <QRCodeButton onPress={handleScanQRCode} disabled={isLoadingQRCode}>
          <QRCodeButtonContent>
            {isLoadingQRCode ? (
              <ActivityIndicator size={40} color={theme.colors.primary} />
            ) : (
              <Ionicons
                name="qr-code-outline"
                size={40}
                color={theme.colors.primary}
              />
            )}
            <QRCodeButtonTextContainer>
              <QRCodeButtonTitle>
                {isLoadingQRCode
                  ? t("common.loading", "Loading...")
                  : t("complaint.form.scan_qr_code")}
              </QRCodeButtonTitle>
              <QRCodeButtonSubtitle>
                {isLoadingQRCode
                  ? t(
                      "complaint.form.processing_qr_code",
                      "Processing QR code..."
                    )
                  : t(
                      "complaint.form.scan_qr_code_subtitle",
                      "Scan QR code to raise a Complaint"
                    )}
              </QRCodeButtonSubtitle>
            </QRCodeButtonTextContainer>
          </QRCodeButtonContent>
        </QRCodeButton>
        {showScanner && (
          <QRScannerModal
            visible={showScanner}
            onClose={() => setShowScanner(false)}
            onScanned={onQRCodeChange}
          />
        )}
      </QRCodeSection>

      <FormInputGroup>
        <FormLabel>{t("complaint.serial_number")}</FormLabel>
        <FormInput
          value={serialNumber}
          onChangeText={onSerialNumberChange}
          placeholder={t("complaint.form.enter_battery_serial")}
          placeholderTextColor={theme.colors.gray}
        />
      </FormInputGroup>
    </>
  );
};
