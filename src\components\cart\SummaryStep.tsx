import React, { useCallback } from "react";
import OrderSummary from "@/components/organisms/OrderSummary";
import CartFooter from "@/components/organisms/CartFooter";
import { useCartManagement } from "@/hooks/useCartManagement";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { useOrderManagement } from "@/hooks/useOrderManagement";
import { VendorDetails } from "@/types/vendor";
import { PaymentMethod } from "@/types/payment";
import * as ImagePicker from "expo-image-picker";

interface SummaryStepProps {
  onBack: () => void;
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  selectedVendor?: VendorDetails | null;
  selectedPaymentMethod?: PaymentMethod | null;
  paymentProof?: ImagePicker.ImagePickerAsset | null;
  selectedPaymentDetails?: {
    account_number?: string | null;
    upi_id?: string | null;
    ifsc_code?: string | null;
    qr_code?: string | null;
  } | null;
}

const SummaryStep: React.FC<SummaryStepProps> = ({
  onBack,
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  selectedVendor,
  selectedPaymentMethod,
  paymentProof,
  selectedPaymentDetails,
}) => {
  const { cartList } = useCartManagement();
  const addressManagement = useAddressManagement();
  const { isPlacingOrder, handlePlaceOrder } = useOrderManagement();

  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  const getEffectiveShippingAddressId = () => {
    return useBillingAsShipping
      ? selectedBillingAddressId
      : selectedShippingAddressId;
  };

  // Get selected addresses based on current state
  const getSelectedBillingAddress = () => {
    const addresses = addressManagement.getAddresses();
    return (
      addresses.billing.find(
        (addr: any) => addr.id === selectedBillingAddressId
      ) || null
    );
  };

  const getSelectedShippingAddress = () => {
    const addresses = addressManagement.getAddresses();
    const effectiveShippingId = getEffectiveShippingAddressId();
    return (
      addresses.all.find((addr: any) => addr.id === effectiveShippingId) || null
    );
  };

  const { isReadyForCheckout } = addressManagement;

  // Convert cartList to CartSummaryData format
  const cartSummaryData = cartList
    ? {
        sub_total: cartList.sub_total || "0",
        cgst_total: cartList.cgst_total || "0",
        cgst_percentage: cartList.cgst_percentage || "0",
        sgst_total: cartList.sgst_total || "0",
        sgst_percentage: cartList.sgst_percentage || "0",
        igst_total: cartList.igst_total || "0",
        igst_percentage: cartList.igst_percentage || "0",
        total_amount: cartList.total_amount || "0",
        cartItems: (cartList.cartItems as any) || [],
      }
    : null;

  const handlePlaceOrderAction = useCallback(() => {
    handlePlaceOrder(
      selectedVendor?.user_id,
      selectedBillingAddressId,
      getEffectiveShippingAddressId(),
      selectedPaymentMethod,
      paymentProof,
      selectedPaymentDetails,
      isReadyForCheckout
    );
  }, [
    handlePlaceOrder,
    selectedBillingAddressId,
    getEffectiveShippingAddressId,
    isReadyForCheckout,
    selectedPaymentMethod,
    paymentProof,
  ]);

  return (
    <>
      <OrderSummary
        cartData={cartSummaryData}
        billingAddress={getSelectedBillingAddress()}
        shippingAddress={getSelectedShippingAddress()}
        shipToSameAddress={useBillingAsShipping}
        selectedVendor={selectedVendor}
        selectedPaymentMethod={selectedPaymentMethod}
        paymentProof={paymentProof}
        selectedPaymentDetails={selectedPaymentDetails}
      />

      <CartFooter
        onClearCart={onBack}
        onContinue={handlePlaceOrderAction}
        clearButtonTitle="Back"
        continueButtonTitle={
          isPlacingOrder ? "Placing Order..." : "Place Order"
        }
        loading={isPlacingOrder}
        disabled={!isReadyForCheckout()}
      />
    </>
  );
};

export default SummaryStep;
