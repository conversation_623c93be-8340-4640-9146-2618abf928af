import React, { useEffect, useState, useCallback } from "react";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { Loading<PERSON>verlay, Header, OrderTimelineOrganism } from "@/components";
import {
  ResponsiveContainer,
  ResponsiveContent,
  ResponsiveSection,
  ResponsiveTwoColumn,
} from "@/components/layout";
import {
  OrderHeader,
  OrderItems,
  OrderAddresses,
  OrderFinancials,
} from "@/components/organisms";
import { ErrorText, EmptyContainer, EmptyText } from "@/styles/Order.styles";
import { RootState, useAppSelector, useAppDispatch } from "@/store/store";
import { getOrderDetailsAction, orderStatusChangeAction, getOrdersListAction } from "@/store/actions/order";
import { OrderStatusEnum } from "@/types/order";
import { DispatchData } from "@/components/molecules/OrderTimeline";
import Toast from "react-native-toast-message";

const OrderDetailScreen = () => {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Redux state
  const { orderDetails, orderStatusList } = useAppSelector(
    (state: RootState) => state.orders
  );
  // Local state for loading and error
  const [orderDetailsLoading, setOrderDetailsLoading] = useState(false);
  const [orderDetailsError, setOrderDetailsError] = useState<string | null>(
    null
  );

  // Fetch order details on mount or id change
  useEffect(() => {
    const fetchOrderDetails = async (orderId: string) => {
      try {
        setOrderDetailsLoading(true);
        setOrderDetailsError(null);
        await dispatch(
          getOrderDetailsAction({ order_id: Number(orderId) })
        ).unwrap();
      } catch (error: any) {
        setOrderDetailsError(error?.message || t("order.error_loading"));
      } finally {
        setOrderDetailsLoading(false);
      }
    };
    if (id && typeof id === "string") {
      fetchOrderDetails(id);
    }
  }, [id, dispatch, t]);

  // Utility functions
  const getOrderStatusLabel = useCallback(
    (statusId: string | number): string => {
      return (
        orderStatusList?.find((status) => status.id === statusId)
          ?.label || ""
      );
    },
    [orderStatusList]
  );

  const hasOrderDetails = () => {
    return !!orderDetails;
  };

  const handleStatusChange = useCallback(async (orderId: number, status: OrderStatusEnum, reason?: string) => {
    try {
      if (status === OrderStatusEnum.Approved) {
        await dispatch(orderStatusChangeAction({ order_id: orderId, status: OrderStatusEnum.Approved })).unwrap();
      } else if (status === OrderStatusEnum.InDispatch) {
        await dispatch(orderStatusChangeAction({ order_id: orderId, status: OrderStatusEnum.InDispatch })).unwrap();
      } else if (status === OrderStatusEnum.Received) {
        await dispatch(orderStatusChangeAction({ order_id: orderId, status: OrderStatusEnum.Received })).unwrap();
      } else if (status === OrderStatusEnum.Cancelled) {
        await dispatch(orderStatusChangeAction({
          order_id: orderId,
          status: OrderStatusEnum.Cancelled,
          description: reason
        })).unwrap();
      }
      await dispatch(getOrderDetailsAction({ order_id: orderId })).unwrap();
      await dispatch(getOrdersListAction({ page: 1 })).unwrap(); 
    } catch (error) {
    }
  }, [dispatch]);

  const handleDispatchStatusChange = useCallback(async (orderId: number, status: OrderStatusEnum, dispatchData: DispatchData) => {
    try {
      await dispatch(orderStatusChangeAction({
        order_id: orderId,
        status: OrderStatusEnum.Dispatched,
        vehical_no: dispatchData.vehical_no,
        tracking: dispatchData.tracking,
        tracking_url: dispatchData.tracking_url,
        notes: dispatchData.notes,
        courier_name: dispatchData.courier_name,
      })).unwrap();
      await dispatch(getOrderDetailsAction({ order_id: orderId })).unwrap();
      await dispatch(getOrdersListAction({ page: 1 })).unwrap(); 
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to dispatch order",
      });
    }
  }, [dispatch]);

  if (orderDetailsLoading) {
    return <LoadingOverlay isLoading={orderDetailsLoading} />;
  }

  if (orderDetailsError) {
    return (
      <EmptyContainer>
        <ErrorText>{orderDetailsError}</ErrorText>
      </EmptyContainer>
    );
  }

  if (!hasOrderDetails()) {
    return (
      <EmptyContainer>
        <EmptyText>{t("order.not_found")}</EmptyText>
      </EmptyContainer>
    );
  }

  return (
    <>
      <Header
        title={t("order.title")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <ResponsiveContainer>
        <ResponsiveContent showsVerticalScrollIndicator={false}>
          <ResponsiveSection>
            <OrderHeader
              orderId={orderDetails.id}
              orderNumber={orderDetails.order_number}
              orderDate={orderDetails.order_date}
              status={orderDetails.status}
              statusLabel={getOrderStatusLabel(orderDetails.status)}
              totalAmount={orderDetails.total_amount}
              paymentStatus={orderDetails.is_paid}
              orderType={orderDetails.order_type}
            />
          </ResponsiveSection>

          <ResponsiveSection>
            <OrderTimelineOrganism
              status={Number(orderDetails.status)}
              orderStatusList={orderStatusList || []}
              orderId={orderDetails.id}
              onStatusChange={handleStatusChange}
              onDispatchStatusChange={handleDispatchStatusChange}
            />
          </ResponsiveSection>

          <ResponsiveSection>
            <OrderItems orderDetails={orderDetails.order_detail} />
          </ResponsiveSection>

          <ResponsiveTwoColumn
            leftColumn={
              <OrderAddresses
                billingAddress={orderDetails.billing_address}
                shippingAddress={orderDetails.shipping_address}
              />
            }
            rightColumn={
              <OrderFinancials
                subtotal={orderDetails.sub_total}
                cgstPercentage={orderDetails.cgst_percentage}
                sgstPercentage={orderDetails.sgst_percentage}
                igstPercentage={orderDetails.igst_percentage}
                cgstTotal={orderDetails.cgst_total}
                sgstTotal={orderDetails.sgst_total}
                igstTotal={orderDetails.igst_total}
                discountAmount={orderDetails.discount_amount}
                totalAmount={orderDetails.total_amount}
              />
            }
          />
        </ResponsiveContent>
      </ResponsiveContainer>
    </>
  );
};

export default OrderDetailScreen;
