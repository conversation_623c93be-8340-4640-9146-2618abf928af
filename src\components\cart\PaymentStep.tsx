import React from "react";
import { ScrollView } from "react-native";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";
import { PaymentMethod, PaymentStepProps } from "@/types/payment";
import { usePayment } from "@/hooks/usePayment";

// Components
import CartFooter from "@/components/organisms/CartFooter";
import PaymentMethodCard from "@/components/molecules/PaymentMethodCard";
import PaymentDetails from "@/components/organisms/PaymentDetails";
import PaymentProofUploader from "@/components/molecules/PaymentProofUploader";

// Styles
import { Container, Title } from "./PaymentStep.styles";
import Toast from "react-native-toast-message";
import * as ImagePicker from "expo-image-picker";

const PaymentStep: React.FC<PaymentStepProps> = ({ onBack, onContinue }) => {
  const { t } = useTranslation();
  const { user, settingsList } = useAppSelector((state) => state.auth);

  const isVendor = user?.role_id === UserType.VENDOR;

  const {
    selectedMethod,
    paymentProof: currentPaymentProof,
    handleMethodSelect,
    handleCopyToClipboard,
    handleDownloadQR,
    handleRemovePaymentProof,
    isPaymentProofRequired,
    canContinue,
    setPaymentProof,
  } = usePayment();

  // Get payment methods from settingsList or use mock data for now
  const paymentMethodsData = (settingsList as any)?.payment_details;

  // Get payment type mapping from settingsList or fallback
  const paymentTypeMap = settingsList?.payment_type;

  // Helper function to get IFSC code for Account_Number
  const getIFSCCode = () => {
    const ifscEntry = paymentMethodsData?.find(
      (method) => method.identified_key === "IFSC_Code"
    );
    return ifscEntry?.identifier_value;
  };

  // Map payment methods data to component format
  const getPaymentMethodIcon = (identifiedKey: string) => {
    switch (identifiedKey) {
      case "Account_Number":
        return "card-outline" as const;
      case "UPI_ID":
        return "phone-portrait-outline" as const;
      case "QR_CODE":
        return "qr-code-outline" as const;
      case "Credit":
        return "wallet-outline" as const;
      default:
        return "card-outline" as const;
    }
  };

  const getPaymentMethodEnum = (identifiedKey: string): PaymentMethod => {
    switch (identifiedKey) {
      case "Account_Number":
        return PaymentMethod.Account_Number;
      case "UPI_ID":
        return PaymentMethod.UPI_Id;
      case "QR_CODE":
        return PaymentMethod.QR_Code;
      case "Credit":
        return PaymentMethod.Credit;
      default:
        return PaymentMethod.Account_Number;
    }
  };

  const getPaymentMethodLabel = (identifiedKey: string) => {
    switch (identifiedKey) {
      case "Account_Number":
        return t("cart.payment.methods.bank_account");
      case "UPI_ID":
        return t("cart.payment.methods.upi_id");
      case "QR_CODE":
        return t("cart.payment.methods.qr_code");
      case "Credit":
        return t("cart.payment.methods.credit");
      default:
        return identifiedKey;
    }
  };

  const getPaymentMethodDescription = (identifiedKey: string) => {
    switch (identifiedKey) {
      case "Account_Number":
        return t("cart.payment.methods.bank_account_desc");
      case "UPI_ID":
        return t("cart.payment.methods.upi_id_desc");
      case "QR_CODE":
        return t("cart.payment.methods.qr_code_desc");
      case "Credit":
        return t("cart.payment.methods.credit_desc");
      default:
        return "";
    }
  };

  // Helper function to get payment type ID with fallback for key mismatches
  const getPaymentTypeId = (identifiedKey: string): number => {
    // Direct match
    if (paymentTypeMap?.[identifiedKey]) {
      return Number(paymentTypeMap[identifiedKey]);
    }

    // Handle backend key mismatches
    switch (identifiedKey) {
      case "UPI_ID":
        return Number(paymentTypeMap?.["UPI_Id"]) || 2; // Fallback to 2 if not found
      case "QR_CODE":
        return Number(paymentTypeMap?.["QR_Code"]) || 3; // Fallback to 3 if not found
      case "Account_Number":
        return Number(paymentTypeMap?.["Account_Number"]) || 1; // Fallback to 1 if not found
      case "Credit":
        return Number(paymentTypeMap?.["Credit"]) || 4; // Fallback to 4 if not found
      default:
        return 1; // Default fallback
    }
  };

  // Filter and transform payment methods
  const paymentMethods =
    paymentMethodsData
      ?.filter((method) => {
        // Filter out Credit for vendors
        if (method.identified_key === "Credit" && !isVendor) {
          return false;
        }
        // Filter out IFSC_Code as it will be handled with Account_Number
        if (method.identified_key === "IFSC_Code") {
          return false;
        }
        return true;
      })
      .map((method) => ({
        id: method.identified_key, // Use identified_key as unique id (string)
        payment_type: getPaymentTypeId(method.identified_key), // Get numeric id with fallback
        label: getPaymentMethodLabel(method.identified_key),
        description: getPaymentMethodDescription(method.identified_key),
        icon: getPaymentMethodIcon(method.identified_key),
        details: {
          ...method,
          accountNumber:
            method.identified_key === "Account_Number"
              ? method.identifier_value
              : undefined,
          ifscCode:
            method.identified_key === "Account_Number"
              ? getIFSCCode()
              : undefined,
          upiId:
            method.identified_key === "UPI_ID"
              ? method.identifier_value
              : undefined,
          qrImageUrl:
            method.identified_key === "QR_CODE"
              ? method.qr_code?.url
              : undefined,
        },
      })) || [];

  const handleContinue = () => {
    if (!selectedMethod) {
      Toast.show({
        type: "error",
        text1: t("cart.payment.errors.select_method"),
      });
      return;
    }

    if (isPaymentProofRequired && !currentPaymentProof) {
      Toast.show({
        type: "error",
        text1: t("cart.payment.errors.upload_proof"),
      });
      return;
    }

    const selectedPaymentMethod = paymentMethods.find(
      (method) => method.payment_type === selectedMethod
    );

    let paymentDetails = null;

    if (selectedPaymentMethod) {
      const details = selectedPaymentMethod.details;

      paymentDetails = {
        account_number:
          selectedMethod === PaymentMethod.Account_Number
            ? details.identifier_value
            : null,
        upi_id:
          selectedMethod === PaymentMethod.UPI_Id
            ? details.identifier_value
            : null,
        ifsc_code:
          selectedMethod === PaymentMethod.Account_Number
            ? details.ifscCode
            : null,
        qr_code:
          selectedMethod === PaymentMethod.QR_Code ? details.qrImageUrl : null,
      };
    }

    onContinue(
      selectedMethod,
      currentPaymentProof || undefined,
      paymentDetails
    );
  };

  const handlePaymentProofUpload = async (
    asset: ImagePicker.ImagePickerAsset
  ) => {
    // The PaymentProofUploader already validates the file
    // Just set it directly here
    setPaymentProof(asset);
  };

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ flex: 1, paddingHorizontal: 16 }}
        contentContainerStyle={{ paddingBottom: 24 }}
      >
        <Title>{t("cart.payment.title")}</Title>

        {paymentMethods.map((method) => (
          <PaymentMethodCard
            key={method.id}
            id={method.id}
            label={method.label}
            description={method.description}
            icon={method.icon}
            selected={selectedMethod === method.payment_type}
            onSelect={() => handleMethodSelect(method.payment_type)}
          >
            {method.payment_type !== PaymentMethod.Credit && (
              <PaymentDetails
                method={getPaymentMethodEnum(method.id)}
                onCopy={handleCopyToClipboard}
                onDownload={() => handleDownloadQR(method.details.qrImageUrl)}
                paymentData={method.details}
              />
            )}
          </PaymentMethodCard>
        ))}

        {isPaymentProofRequired && (
          <PaymentProofUploader
            paymentProof={currentPaymentProof}
            onUpload={handlePaymentProofUpload}
            onRemove={handleRemovePaymentProof}
            required={true}
          />
        )}
      </ScrollView>

      <CartFooter
        onClearCart={onBack}
        onContinue={handleContinue}
        clearButtonTitle={t("cart.payment.buttons.back")}
        continueButtonTitle={t("cart.payment.buttons.continue")}
        disabled={!canContinue}
      />
    </Container>
  );
};

export default PaymentStep;
