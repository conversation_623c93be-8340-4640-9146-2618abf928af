import React from "react";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  SelectionCard,
  SelectionIcon,
  SelectionContent,
  SelectionTitle,
  SelectionSubtitle,
} from "@/styles/Complaint.styles";
import { complaintTypeOptions } from "@/utils/common";

interface ComplaintTypeOption {
  id: number;
  name: string;
  value: "vendor" | "customer";
  icon: string;
}

interface ComplaintTypeSelectorProps {
  selectedType: "vendor" | "customer";
  onTypeSelect: (type: "vendor" | "customer") => void;
}

export const ComplaintTypeSelector: React.FC<ComplaintTypeSelectorProps> = ({
  selectedType,
  onTypeSelect,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <>
      {complaintTypeOptions?.map((option) => (
        <SelectionCard
          key={option.id}
          isSelected={selectedType === option.value}
          onPress={() => onTypeSelect(option.value as "vendor" | "customer")}
        >
          <SelectionIcon>
            <Ionicons
              name={option.icon as any}
              size={20}
              color={theme.colors.gray}
            />
          </SelectionIcon>
          <SelectionContent>
            <SelectionTitle isSelected={selectedType === option.value}>
              {option.name}
            </SelectionTitle>
            <SelectionSubtitle>
              {option.value === "vendor"
                ? t("complaint.types.vendor_desc")
                : t("complaint.types.customer_desc")}
            </SelectionSubtitle>
          </SelectionContent>
          {selectedType === option.value && (
            <Ionicons
              name="checkmark-circle"
              size={24}
              color={theme.colors.primary}
            />
          )}
        </SelectionCard>
      ))}
    </>
  );
};
