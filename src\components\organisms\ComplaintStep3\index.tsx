import React, { useCallback, useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  Step<PERSON>ontainer,
  StepHeader,
  StepTitle,
  StepSubtitle,
} from "@/styles/Complaint.styles";
import AddressSelection from "@/components/organisms/AddressSelection";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { VendorDetails } from "@/types/vendor";
import { Customer } from "@/types/customer";
import Toast from "react-native-toast-message";

interface ComplaintStep3Props {
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  setSelectedBillingAddressId?: (id: number | null) => void;
  setSelectedShippingAddressId?: (id: number | null) => void;
  setUseBillingAsShipping?: (value: boolean) => void;
  selectedVendor?: VendorDetails | null;
  selectedCustomer?: Customer | null;
}

const ComplaintStep3: React.FC<ComplaintStep3Props> = ({
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  setSelectedBillingAddressId: externalSetBillingId,
  setSelectedShippingAddressId: externalSetShippingId,
  setUseBillingAsShipping: externalSetUseBillingAsShipping,
  selectedVendor,
  selectedCustomer,
}) => {
  const router = useRouter();
  const addressManagement = useAddressManagement();
  const { t } = useTranslation();
  const { theme } = useTheme();

  // Use external state if provided, otherwise fall back to hook state
  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  // Use external setters if provided, otherwise fall back to hook handlers
  const handleBillingAddressSelection = externalSetBillingId
    ? (address: any) => externalSetBillingId(address.id)
    : addressManagement.handleBillingAddressSelection;

  const handleShippingAddressSelection = externalSetShippingId
    ? (address: any) => externalSetShippingId(address.id)
    : addressManagement.handleShippingAddressSelection;

  const handleToggleBillingAsShipping = externalSetUseBillingAsShipping
    ? (value: boolean) => externalSetUseBillingAsShipping(value)
    : addressManagement.handleToggleBillingAsShipping;

  const { handleEditAddress, getAddresses, isReadyForCheckout } =
    addressManagement;

  // Always use current user's addresses
  const addresses = getAddresses().all;
  const hasNoAddresses = addresses.length === 0;

  const getValidationStatus = useCallback(() => {
    if (hasNoAddresses) {
      return {
        isValid: false,
        message: t("complaint.address_validation.no_addresses"),
      };
    }

    const hasBillingAddress = !!selectedBillingAddressId;
    const hasShippingAddress = !!selectedShippingAddressId;

    if (!hasBillingAddress) {
      return {
        isValid: false,
        message: t("complaint.address_validation.billing_required"),
      };
    }

    if (!useBillingAsShipping && !hasShippingAddress) {
      return {
        isValid: false,
        message: t("complaint.address_validation.shipping_required"),
      };
    }

    return { isValid: true, message: "" };
  }, [
    hasNoAddresses,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    t,
  ]);

  const validationStatus = getValidationStatus();

  const handleAddNewAddress = () => {
    if (selectedVendor) {
      router.push(
        `/(protected)/add-address?vendorId=${selectedVendor.vendor_id}`
      );
    } else if (selectedCustomer) {
      router.push(
        `/(protected)/add-address?customerId=${selectedCustomer.user_id}`
      );
    } else {
      router.push(`/(protected)/add-address`);
    }
  };

  const getEntityName = () => {
    if (selectedVendor) {
      return (
        selectedVendor.name ||
        selectedVendor.first_name + " " + selectedVendor.last_name
      );
    } else if (selectedCustomer) {
      return (
        selectedCustomer.name ||
        selectedCustomer.first_name + " " + selectedCustomer.last_name
      );
    }
    return t("complaint.form.current_user");
  };

  if (hasNoAddresses) {
    return (
      <StepContainer>
        <StepHeader>
          <StepTitle>{t("complaint.form.select_address")}</StepTitle>
          <StepSubtitle>
            {t("complaint.form.no_addresses_found_for_entity", {
              entity: getEntityName(),
            })}
          </StepSubtitle>
        </StepHeader>

        <AddressSelection
          addresses={addresses}
          selectedBillingId={selectedBillingAddressId}
          selectedShippingId={selectedShippingAddressId}
          onSelectBilling={handleBillingAddressSelection}
          onSelectShipping={handleShippingAddressSelection}
          onEditAddress={handleEditAddress}
          useBillingAsShipping={useBillingAsShipping}
          onToggleBillingAsShipping={handleToggleBillingAsShipping}
          selectedVendor={selectedVendor}
          onAddNewAddress={handleAddNewAddress}
        />
      </StepContainer>
    );
  }

  return (
    <StepContainer>
      <StepHeader>
        <StepTitle>{t("complaint.form.select_address")}</StepTitle>
        <StepSubtitle>
          {t("complaint.form.choose_complaint_address_for_entity", {
            entity: getEntityName(),
          })}
        </StepSubtitle>
      </StepHeader>

      <AddressSelection
        addresses={addresses}
        selectedBillingId={selectedBillingAddressId}
        selectedShippingId={selectedShippingAddressId}
        onSelectBilling={handleBillingAddressSelection}
        onSelectShipping={handleShippingAddressSelection}
        onEditAddress={handleEditAddress}
        useBillingAsShipping={useBillingAsShipping}
        onToggleBillingAsShipping={handleToggleBillingAsShipping}
        selectedVendor={selectedVendor}
        onAddNewAddress={handleAddNewAddress}
      />
    </StepContainer>
  );
};

export default ComplaintStep3;
