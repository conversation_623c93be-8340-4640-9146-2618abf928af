import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  StepHeader,
  StepTitle,
  StepSubtitle,
  FormInputGroup,
  FormLabel,
  FormTextArea,
  IssueSelectionCard,
  IssueIconContainer,
  IssueContentContainer,
  IssueTitleText,
  ChevronIcon,
  RequiredText,
} from "@/styles/Complaint.styles";
import { BatteryBrandSelector } from "@/components/molecules/BatteryBrandSelector";
import { OzoneBatteryForm } from "@/components/molecules/OzoneBatteryForm";
import { OtherBrandForm } from "@/components/molecules/OtherBrandForm";
import PickerBottomSheet from "@/components/molecules/PickerField/PickerBottomSheet";
import { Ionicons } from "@expo/vector-icons";
import { RootState, useAppSelector } from "@/store/store";

interface ComplaintStep2Props {
  batteryBrand: "ozone" | "other";
  serialNumber: string;
  qrCode: string;
  productImages: string[];
  brandName: string;
  description: string;
  selectedIssue: string;
  isLoadingQRCode?: boolean;
  onBatteryBrandChange: (brand: "ozone" | "other") => void;
  onSerialNumberChange: (value: string) => void;
  onQRCodeChange: (value: string) => void;
  onProductImagesChange: (images: string[]) => void;
  onBrandNameChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onIssueSelect: (issueId: string) => void;
}

export const ComplaintStep2: React.FC<ComplaintStep2Props> = ({
  batteryBrand,
  serialNumber,
  productImages,
  brandName,
  description,
  selectedIssue,
  isLoadingQRCode = false,
  onBatteryBrandChange,
  onSerialNumberChange,
  onQRCodeChange,
  onProductImagesChange,
  onBrandNameChange,
  onDescriptionChange,
  onIssueSelect,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [showIssueBottomSheet, setShowIssueBottomSheet] = useState(false);
  const { complaintIssues } = useAppSelector(
    (state: RootState) => state.complaints
  );
  const selectedIssueDisplay = complaintIssues.find(
    (issue) => issue.id === Number(selectedIssue)
  );

  const handleIssueSelect = async (item: { id: number; title: string }) => {
    onIssueSelect(item.id.toString());
    setShowIssueBottomSheet(false);
  };

  const handleOpenBottomSheet = () => {
    setShowIssueBottomSheet(true);
  };

  const handleCloseBottomSheet = () => {
    setShowIssueBottomSheet(false);
  };

  return (
    <>
      {/* Complaint Issue Selection */}
      <StepContainer>
        <StepHeader>
          <StepTitle>{t("complaint.form.select_issue")}</StepTitle>
          <StepSubtitle>{t("complaint.form.choose_issue_type")}</StepSubtitle>
        </StepHeader>

        <IssueSelectionCard
          isSelected={!!selectedIssue}
          onPress={handleOpenBottomSheet}
        >
          <IssueIconContainer isSelected={!!selectedIssue}>
            <Ionicons
              name="alert-circle-outline"
              size={24}
              color={selectedIssue ? theme.colors.white : theme.colors.gray}
            />
          </IssueIconContainer>

          <IssueContentContainer>
            <IssueTitleText isSelected={!!selectedIssue}>
              {selectedIssue
                ? selectedIssueDisplay?.name || t("complaint.form.select_issue")
                : t("complaint.form.select_issue")}
            </IssueTitleText>
          </IssueContentContainer>

          <ChevronIcon>
            <Ionicons name="chevron-down" size={20} color={theme.colors.gray} />
          </ChevronIcon>
        </IssueSelectionCard>
      </StepContainer>

      {/* Battery brand selection */}
      <StepContainer>
        <StepHeader>
          <StepTitle>{t("complaint.form.select_battery_brand")}</StepTitle>
          <StepSubtitle>
            {t("complaint.form.choose_battery_brand")}
          </StepSubtitle>
        </StepHeader>

        <BatteryBrandSelector
          selectedBrand={batteryBrand}
          onBrandSelect={onBatteryBrandChange}
        />
      </StepContainer>

      {/* Product details based on battery brand */}
      {batteryBrand === "ozone" && (
        <StepContainer>
          <StepHeader>
            <StepTitle>{t("complaint.form.ozone_battery_details")}</StepTitle>
            <StepSubtitle>
              {t("complaint.form.scan_qr_enter_serial")}
            </StepSubtitle>
          </StepHeader>

          <OzoneBatteryForm
            serialNumber={serialNumber}
            onQRCodeChange={onQRCodeChange}
            onSerialNumberChange={onSerialNumberChange}
            isLoadingQRCode={isLoadingQRCode}
          />
        </StepContainer>
      )}

      {batteryBrand === "other" && (
        <StepContainer>
          <StepHeader>
            <StepTitle>{t("complaint.form.other_brand_details")}</StepTitle>
            <StepSubtitle>
              {t("complaint.form.upload_image_enter_brand")}
            </StepSubtitle>
          </StepHeader>

          <OtherBrandForm
            productImages={productImages}
            brandName={brandName}
            onProductImagesChange={onProductImagesChange}
            onBrandNameChange={onBrandNameChange}
          />
        </StepContainer>
      )}

      <StepContainer>
        <FormInputGroup>
          <FormLabel>
            {t("complaint.description")}
            {!selectedIssue && <RequiredText> *</RequiredText>}
          </FormLabel>
          <FormTextArea
            value={description}
            onChangeText={onDescriptionChange}
            placeholder={t("complaint.form.add_additional_details_placeholder")}
            placeholderTextColor={theme.colors.gray}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </FormInputGroup>
      </StepContainer>

      {/* Issue Picker Bottom Sheet */}
      <PickerBottomSheet
        isVisible={showIssueBottomSheet}
        onClose={handleCloseBottomSheet}
        onSelect={handleIssueSelect}
        data={complaintIssues}
        title={t("complaint.form.select_issue")}
        searchable={true}
      />
    </>
  );
};
