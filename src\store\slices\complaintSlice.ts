import { createSlice } from "@reduxjs/toolkit";
import { ComplaintState } from "../../types/complaint";
import {
  getComplaintsListAction,
  complaintInsertUpdateAction,
  getComplaintStatusAction,
  getComplaintIssueListAction,
} from "../actions/complaint";
import { logoutAction } from "../actions/auth";
import { logoutLocalAction } from "../actions/auth";

const initialState: ComplaintState = {
  complaints: [],
  complaintDetails: null,
  loading: false,
  error: null,
  complaintsLoading: false,
  isLoadingMore: false,
  filters: null,
  current_page: 1,
  last_page: 1,
  loadedPages: [],
  complaintStatus: [],
  complaintIssues: [],
};

const complaintSlice = createSlice({
  name: "complaint",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    resetFilters: (state) => {
      state.filters = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all complaints
      .addCase(getComplaintsListAction.pending, (state, action) => {
        const isPagination = action.meta.arg?.page && action.meta.arg.page > 1;
        if (isPagination) {
          state.isLoadingMore = true;
        } else {
          state.complaintsLoading = true;
        }
        state.error = null;
      })
      .addCase(getComplaintsListAction.fulfilled, (state, action) => {
        state.complaintsLoading = false;
        state.isLoadingMore = false;
        state.filters = action.payload.meta;
        const responseData = action.payload?.data;
        if (responseData) {
          state.current_page = responseData.current_page;
          state.last_page = responseData.last_page;

          if (!state.loadedPages.includes(responseData.current_page)) {
            state.loadedPages.push(responseData.current_page);
          }

          if (responseData.current_page === 1) {
            state.complaints = responseData.data;
            state.loadedPages = [1];
          } else {
            state.complaints = [...state.complaints, ...responseData.data];
          }
        }
      })
      .addCase(getComplaintsListAction.rejected, (state, action) => {
        state.complaintsLoading = false;
        state.isLoadingMore = false;
        state.error = action.error.message || "Failed to load complaints";
      })
      // Create complaint
      .addCase(complaintInsertUpdateAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(complaintInsertUpdateAction.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload && action.payload.data) {
          state.complaints.unshift(action.payload.data);
        }
      })
      .addCase(complaintInsertUpdateAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create complaint";
      })
      // Get complaint status
      .addCase(getComplaintStatusAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintStatusAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaintStatus = action.payload.data;
      })
      // Get complaint issue list
      .addCase(getComplaintIssueListAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintIssueListAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaintIssues = action.payload.data;
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      });
  },
});

export const { setFilters, resetFilters } = complaintSlice.actions;

export default complaintSlice.reducer;
