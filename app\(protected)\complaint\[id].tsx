import React, { useCallback, useEffect, useState } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { complaintStatusChangeAction } from "@/store/actions/complaint";
import { ScrollView } from "react-native";
import {
  Container,
  Content,
  Card,
  Title,
  Label,
  Value,
  StatusBadge,
  StatusText,
  ErrorText,
  EmptyContainer,
  EmptyText,
  ImageContainer,
  ComplaintImage,
  ActionButton,
  ActionButtonText,
  ActionButtonContainer,
  FullScreenModal,
  FullScreenImageContainer,
  FullScreenImage,
  CloseButton,
  SectionTitle,
  SectionCard,
  InfoRow,
  InfoLabel,
  InfoValue,
  StatusContainer,
  HistoryItem,
  HistoryText,
  HistoryDate,
} from "@/styles/Complaint.styles";
import { Header } from "@/components";
import { UserType } from "@/types/api";
import { ComplaintStatusEnum } from "@/types/complaint";
import Toast from "react-native-toast-message";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";

const ComplaintDetailScreen = () => {
  const { id } = useLocalSearchParams();
  const { t } = useTranslation();
  const { complaints, complaintStatus } = useAppSelector(
    (state) => state.complaints
  );
  const user = useAppSelector((state) => state.auth.user);
  const isSalesPerson = UserType.SALESPERSON === user.role_id;
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();
  const complaintDetails = complaints.find(
    (complaint) => String(complaint.id) === String(id)
  ) as any;

  const getNextStatusOptions = (
    currentStatus: ComplaintStatusEnum,
    userRole: UserType
  ) => {
    switch (currentStatus) {
      case ComplaintStatusEnum.InReview:
        if (userRole === UserType.SERVICEPERSON) {
          return [
            ComplaintStatusEnum.EstimationSent,
            ComplaintStatusEnum.Rejected,
          ];
        }
        return [];
      case ComplaintStatusEnum.EstimationSent:
        if (
          userRole === UserType.CUSTOMER ||
          userRole === UserType.SALESPERSON ||
          userRole === UserType.VENDOR
        ) {
          return [ComplaintStatusEnum.Approved, ComplaintStatusEnum.Rejected];
        }
        return [];
      case ComplaintStatusEnum.Approved:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.RepairInProgress];
        }
        return [];
      case ComplaintStatusEnum.RepairInProgress:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.Resolved];
        }
        return [];
      default:
        return [];
    }
  };

  const handleComplaintStatusChange = async (status: ComplaintStatusEnum) => {
    if (!complaintDetails) return;
    try {
      setIsLoading(true);
      await dispatch(
        complaintStatusChangeAction({
          complaint_id: complaintDetails.id,
          status,
        })
      ).unwrap();
      Toast.show({ type: "success", text1: t("complaint.status_updated") });
    } catch (err: any) {
      Toast.show({ type: "error", text1: t("error_updating_status") });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusLabel = (status: number) => {
    return complaintStatus.find(
      (statusItem) => statusItem.id === Number(status)
    )?.label;
  };
  if (!complaintDetails) {
    return (
      <>
        <Header
          title={t("complaint.title")}
          showBack
          onBackPress={() => router.back()}
          showCart={false}
        />
        <Container>
          <EmptyContainer>
            <EmptyText>{t("complaint.not_found")}</EmptyText>
          </EmptyContainer>
        </Container>
      </>
    );
  }

  return (
    <>
      <Header
        title={t("complaint.title")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <Container>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Content>
            <SectionCard>
              <Title>{complaintDetails?.complaint_number}</Title>

              <StatusContainer>
                <Label>{t("complaint.status")}</Label>
                <StatusBadge status={complaintDetails?.status || 1}>
                  <StatusText status={complaintDetails?.status || 1}>
                    {getStatusLabel(complaintDetails?.status || 1)}
                  </StatusText>
                </StatusBadge>
              </StatusContainer>

              <InfoRow>
                <InfoLabel>{t("complaint.type")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.complaint_type === "1"
                    ? t("complaint.brands.ozone")
                    : t("complaint.brands.third_party")}
                </InfoValue>
              </InfoRow>

              <InfoRow>
                <InfoLabel>{t("complaint.created_by")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.created_by?.name ||
                    t("common.not_available")}
                </InfoValue>
              </InfoRow>

              <InfoRow>
                <InfoLabel>{t("complaint.created_at")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.created_at
                    ? new Date(complaintDetails.created_at).toLocaleDateString()
                    : t("common.not_available")}
                </InfoValue>
              </InfoRow>
            </SectionCard>

            {/* Complaint Details Section */}
            <SectionCard>
              <SectionTitle>{t("complaint.details")}</SectionTitle>

              {complaintDetails?.description && (
                <InfoRow>
                  <InfoLabel>{t("complaint.description")}</InfoLabel>
                  <InfoValue>{complaintDetails.description}</InfoValue>
                </InfoRow>
              )}

              {complaintDetails?.complaint_issue && (
                <InfoRow>
                  <InfoLabel>{t("complaint.issue")}</InfoLabel>
                  <InfoValue>{complaintDetails.complaint_issue.name}</InfoValue>
                </InfoRow>
              )}
            </SectionCard>

            {/* Product Information Section */}
            {(complaintDetails?.product_qr_code ||
              complaintDetails?.brand_name) && (
              <SectionCard>
                <SectionTitle>{t("complaint.product_info")}</SectionTitle>

                {complaintDetails?.product_qr_code && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.serial_number")}</InfoLabel>
                    <InfoValue>
                      {complaintDetails.product_qr_code.serial_no}
                    </InfoValue>
                  </InfoRow>
                )}

                {complaintDetails?.brand_name && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.brand_name")}</InfoLabel>
                    <InfoValue>{complaintDetails.brand_name}</InfoValue>
                  </InfoRow>
                )}
              </SectionCard>
            )}

            {/* Product Photos Section */}
            {complaintDetails?.product_serial_photo &&
              complaintDetails.product_serial_photo.length > 0 && (
                <SectionCard>
                  <SectionTitle>{t("complaint.product_photos")}</SectionTitle>
                  <ImageContainer>
                    {complaintDetails.product_serial_photo.map(
                      (photo: any, index: number) => (
                        <ComplaintImage
                          key={photo.id}
                          source={{ uri: photo.url }}
                          contentFit="contain"
                          onPress={() => setSelectedImage(photo.url)}
                        />
                      )
                    )}
                  </ImageContainer>
                </SectionCard>
              )}

            {/* Address Information Section */}
            {(complaintDetails?.billing_address_id ||
              complaintDetails?.shipping_address_id) && (
              <SectionCard>
                <SectionTitle>{t("complaint.addresses")}</SectionTitle>

                {complaintDetails?.billing_address_id && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.billing_address_id")}</InfoLabel>
                    <InfoValue>{complaintDetails.billing_address_id}</InfoValue>
                  </InfoRow>
                )}

                {complaintDetails?.shipping_address_id && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.shipping_address_id")}</InfoLabel>
                    <InfoValue>
                      {complaintDetails.shipping_address_id}
                    </InfoValue>
                  </InfoRow>
                )}
              </SectionCard>
            )}

            {complaintDetails?.histories &&
              complaintDetails.histories.length > 0 && (
                <SectionCard>
                  <SectionTitle>{t("complaint.history")}</SectionTitle>
                  {complaintDetails.histories.map(
                    (history: any, index: number) => (
                      <HistoryItem key={index}>
                        <HistoryText>
                          {t("complaint.status_changed_to")}{" "}
                          {getStatusLabel(history.new_status)}
                        </HistoryText>
                        <HistoryDate>
                          {new Date(history.created_at).toLocaleDateString()}
                        </HistoryDate>
                      </HistoryItem>
                    )
                  )}
                </SectionCard>
              )}
            {user?.role_id &&
              getNextStatusOptions(complaintDetails.status, user.role_id)
                .length > 0 && (
                <>
                  <Label>{t("complaint.update_status")}</Label>
                  <ActionButtonContainer>
                    {getNextStatusOptions(complaintDetails.status, user.role_id).map(
                      (status) => (
                        <ActionButton
                          key={status}
                          onPress={() => handleComplaintStatusChange(status)}
                        >
                          <ActionButtonText>
                            {t(`status.${status}`)}
                          </ActionButtonText>
                        </ActionButton>
                      )
                    )}
                  </ActionButtonContainer>
                </>
              )}
          </Content>
        </ScrollView>
      </Container>

      <FullScreenModal
        visible={!!selectedImage}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setSelectedImage(null)}
      >
        <FullScreenImageContainer>
          {selectedImage && (
            <FullScreenImage
              source={{ uri: selectedImage }}
              contentFit="contain"
            />
          )}
          <CloseButton onPress={() => setSelectedImage(null)}>
            <Ionicons name="close" size={32} color={theme.colors.black} />
          </CloseButton>
        </FullScreenImageContainer>
      </FullScreenModal>
    </>
  );
};

export default ComplaintDetailScreen;
