import React, { useEffect, useState, useCallback } from "react";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import {
  getComplaintsListAction,
  getComplaintStatusAction,
  getComplaintIssueListAction,
} from "@/store/actions/complaint";
import {
  Container,
  Content,
  ErrorText,
  EmptyContainer,
  EmptyText,
  FloatingActionButton,
  Header,
} from "@/styles/Complaint.styles";
import { Ionicons } from "@expo/vector-icons";
import { LoadingOverlay, SearchBar, ComplaintCard } from "@/components";
import { useTheme } from "@/hooks/useTheme";
import { FlatList } from "react-native";
import { getCustomerListAction } from "@/store/actions/customer";
import { UserType } from "@/types/api";
import { ComplaintListPayload } from "@/types/complaint";
import { useDebounce } from "@/utils/useDebounce";
import { useRef } from "react";

const ComplaintsScreen = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { theme } = useTheme();

  // Redux state
  const {
    complaints = [],
    complaintStatus,
    complaintsLoading,
    isLoadingMore,
    filters,
    current_page,
    last_page,
  } = useAppSelector((state: RootState) => state.complaints);

  const { user } = useAppSelector((state: RootState) => state.auth);
  const isServicePerson = user?.role_id === UserType.SERVICEPERSON;

  // Local state
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 300);

  // Fetch complaints list from API
  const fetchComplaints = useCallback(
    async (params: ComplaintListPayload) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        await dispatch(
          getComplaintsListAction({ ...filters, ...params })
        ).unwrap();
      } catch (error: any) {
        console.error(error);
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    const initializeData = async () => {
      try {
        if (user?.role_id === UserType.SALESPERSON) {
          await dispatch(getCustomerListAction({})).unwrap();
        }
        await dispatch(getComplaintStatusAction({})).unwrap();
        await dispatch(getComplaintIssueListAction({})).unwrap();
        await fetchComplaints({ page: 1 });
      } catch (err) {
        console.log(err);
      }
    };

    initializeData();
  }, []);

  // Handlers
  const handleRefresh = useCallback(() => {
    fetchComplaints({
      page: 1,
    });
  }, [fetchComplaints]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && current_page < last_page) {
      fetchComplaints({
        page: current_page + 1,
      });
    }
  }, [isLoadingMore, current_page, last_page, fetchComplaints]);

  const handleSearch = useCallback(
    async (text: string) => {
      await fetchComplaints({
        page: 1,
        search: text,
      });
    },
    [fetchComplaints]
  );

  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length > 3) {
      handleSearch(debouncedSearch);
    } else if (debouncedSearch === "") {
      // Reset search when cleared
      handleSearch("");
    }
  }, [debouncedSearch]);

  const handleStatusPress = () => {
    console.log("handleStatusPress");
  };

  const getStatusLabel = (state: number) => {
    return complaintStatus.find((status) => status.id === Number(state))?.label;
  };

  if (
    complaintsLoading &&
    !isRefreshing &&
    !isLoadingMore &&
    current_page === 1
  ) {
    return <LoadingOverlay isLoading={complaintsLoading} />;
  }

  const handleComplaintPress = (complaint: any) => {
    router.push(`/complaint/${complaint.id}`);
  };

  const renderComplaintItem = ({ item: complaint }: { item: any }) => {
    return (
      <ComplaintCard
        complaint={complaint}
        getStatusLabel={getStatusLabel}
        onPress={handleComplaintPress}
      />
    );
  };

  return (
    <Container>
      <Header>
        <SearchBar
          onSearch={setSearch}
          onFilterPress={handleStatusPress}
          value={search}
        />
      </Header>
      <Content>
        <FlatList
          data={complaints || []}
          renderItem={renderComplaintItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
          onRefresh={handleRefresh}
          refreshing={isRefreshing}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoadingMore ? <LoadingOverlay isLoading /> : null
          }
          ListEmptyComponent={
            <EmptyContainer>
              <EmptyText>{t("complaint.no_complaints")}</EmptyText>
            </EmptyContainer>
          }
        />
      </Content>
      {!isServicePerson && (
      <FloatingActionButton
        onPress={() => {
          router.push("/complaint/new");
        }}
      >
        <Ionicons name="add" size={24} color={theme.colors.white} />
      </FloatingActionButton>
      )}
    </Container>
  );
};

export default ComplaintsScreen;
